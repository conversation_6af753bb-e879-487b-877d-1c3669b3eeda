import React, { useState, useEffect, useCallback } from 'react';
import { User } from '@supabase/supabase-js';
import { Link } from 'react-router-dom';
import {
  Bell,
  Search,
  Sun,
  Moon,
  MessageCircle,
  Command,
  X
} from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '../ui/dropdown-menu';
import { Badge } from '../ui/badge';
import { Notification } from '../../types/dashboard';
import { useTheme } from '../../hooks/use-theme';
import { useSupabaseUserStore } from '../../stores/supabaseUserStore'; // Import the store

interface DashboardHeaderProps {
  user: User;
  notifications: Notification[];
  onSearch: (query: string) => void;
  onThemeToggle: () => void;
  onNotificationClick: (notification: Notification) => void;
  onDiscussionToggle: () => void;
  isDiscussionOpen: boolean;
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  user,
  notifications,
  onSearch,
  onThemeToggle,
  onNotificationClick,
  onDiscussionToggle,
  isDiscussionOpen
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const { theme, toggleTheme } = useTheme();
  const { userProfile } = useSupabaseUserStore(); // Access userProfile from the store
  
  const unreadCount = notifications.filter(n => !n.isRead).length;
  // Use userProfile.display_name if available, otherwise fallback to existing logic
  const displayName = userProfile?.display_name || user.user_metadata?.full_name || user.user_metadata?.name || user.email?.split('@')[0] || 'User';

  // Keyboard shortcut for search (Ctrl+K or Cmd+K)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.getElementById('global-search') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
          setIsSearchFocused(true);
        }
      }
      
      // Escape to clear search
      if (e.key === 'Escape' && isSearchFocused) {
        setSearchQuery('');
        setIsSearchFocused(false);
        setSearchResults([]);
        const searchInput = document.getElementById('global-search') as HTMLInputElement;
        if (searchInput) {
          searchInput.blur();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isSearchFocused]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((query: string) => {
      if (query.trim()) {
        // Mock search results - replace with actual search implementation
        const mockResults = [
          { id: '1', title: 'Physics Chapter 5', type: 'chapter', url: '/analytics' },
          { id: '2', title: 'Math Assignment', type: 'task', url: '/tasks' },
          { id: '3', title: 'Chemistry Mock Test', type: 'exam', url: '/mock-tests' },
        ].filter(item => 
          item.title.toLowerCase().includes(query.toLowerCase())
        );
        setSearchResults(mockResults);
      } else {
        setSearchResults([]);
      }
    }, 300),
    []
  );

  useEffect(() => {
    debouncedSearch(searchQuery);
  }, [searchQuery, debouncedSearch]);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchQuery);
    setIsSearchFocused(false);
  };

  const handleThemeChange = () => {
    toggleTheme();
    onThemeToggle();
  };



  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-800 shadow-sm">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left Section - Logo & Greeting */}
        <div className="flex items-center gap-6">
          {/* IsotopeAI Logo and Branding */}
          <Link to="/" className="flex items-center gap-3 group hover:opacity-80 transition-opacity">
            <div className="relative w-8 h-8">
              <div className="absolute inset-0 bg-gradient-to-br from-violet-500/20 to-indigo-500/20 rounded-full blur-md group-hover:opacity-100 opacity-0 transition-opacity"></div>
              <img
                src="/icon-192x192.png"
                alt="IsotopeAI Logo"
                className="w-full h-full rounded-full border border-gray-200 dark:border-gray-700 shadow-lg relative z-10 group-hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div className="flex flex-col">
              <span className="font-bold text-lg text-gray-900 dark:text-white tracking-tight">
                IsotopeAI
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                Focus. Track. Achieve.
              </span>
            </div>
          </Link>

          {/* Greeting */}
          <div className="hidden lg:block">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white tracking-tight">
              {getGreeting()}, {displayName}!
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
              Ready to continue your learning journey?
            </p>
          </div>
        </div>

        {/* Center Section - Enhanced Search */}
        <div className="flex-1 max-w-md mx-4 relative">
          <form onSubmit={handleSearchSubmit} className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="global-search"
              type="text"
              placeholder="Search across all content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setTimeout(() => setIsSearchFocused(false), 200)}
              className="pl-10 pr-20 py-2 w-full bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            />
            
            {/* Keyboard shortcut indicator */}
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
              {searchQuery && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSearchQuery('');
                    setSearchResults([]);
                  }}
                  className="h-6 w-6 p-0 hover:bg-gray-200 dark:hover:bg-gray-700"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
              <div className="hidden md:flex items-center gap-1 text-xs text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                <Command className="h-3 w-3" />
                <span>K</span>
              </div>
            </div>
          </form>

          {/* Search Results Dropdown */}
          {isSearchFocused && (searchResults.length > 0 || searchQuery.trim()) && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
              {searchResults.length > 0 ? (
                <>
                  <div className="p-2 border-b border-gray-200 dark:border-gray-700">
                    <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                      Search Results
                    </p>
                  </div>
                  {searchResults.map((result) => (
                    <button
                      key={result.id}
                      onClick={() => {
                        window.location.href = result.url;
                        setIsSearchFocused(false);
                      }}
                      className="w-full text-left p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150 flex items-center gap-3"
                    >
                      <div className="flex-shrink-0">
                        <div className={`w-2 h-2 rounded-full ${
                          result.type === 'chapter' ? 'bg-green-500' :
                          result.type === 'task' ? 'bg-orange-500' :
                          result.type === 'exam' ? 'bg-red-500' : 'bg-blue-500'
                        }`} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {result.title}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                          {result.type}
                        </p>
                      </div>
                    </button>
                  ))}
                </>
              ) : searchQuery.trim() ? (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No results found for "{searchQuery}"</p>
                  <p className="text-xs mt-1">Try different keywords or check spelling</p>
                </div>
              ) : (
                <div className="p-4">
                  <p className="text-xs text-gray-500 dark:text-gray-400 font-medium mb-2">
                    Quick Actions
                  </p>
                  <div className="space-y-1">
                    {[
                      { label: 'View Tasks', url: '/tasks', icon: '📝' },
                      { label: 'Check Analytics', url: '/analytics', icon: '📊' },
                      { label: 'Mock Tests', url: '/mock-tests', icon: '📚' },
                      { label: 'Study Groups', url: '/groups', icon: '👥' },
                    ].map((action) => (
                      <button
                        key={action.label}
                        onClick={() => {
                          window.location.href = action.url;
                          setIsSearchFocused(false);
                        }}
                        className="w-full text-left p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded transition-colors duration-150 flex items-center gap-3"
                      >
                        <span className="text-sm">{action.icon}</span>
                        <span className="text-sm text-gray-700 dark:text-gray-300">{action.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Right Section - Actions & Profile */}
        <div className="flex items-center gap-4 pr-16">
          {/* Discussion Sidebar Toggle */}
          <Button
            variant="ghost"
            size="sm"
            className={`p-2 relative transition-colors duration-200 ${
              isDiscussionOpen 
                ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300' 
                : 'hover:bg-green-50 dark:hover:bg-green-900/20 text-green-600 dark:text-green-400'
            }`}
            onClick={onDiscussionToggle}
            title="Toggle Discussion Sidebar"
          >
            <MessageCircle className="h-5 w-5" />
          </Button>

          {/* Enhanced Theme Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleThemeChange}
            className="p-2 hover:bg-amber-50 dark:hover:bg-amber-900/20 transition-colors duration-200"
            title={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}
          >
            {theme === 'dark' ? (
              <Sun className="h-5 w-5 text-amber-500" />
            ) : (
              <Moon className="h-5 w-5 text-indigo-600" />
            )}
          </Button>

          {/* Enhanced Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm" 
                className="p-2 relative hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-200"
              >
                <Bell className={`h-5 w-5 ${unreadCount > 0 ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'}`} />
                {unreadCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs p-0 min-w-[20px] bg-red-500 hover:bg-red-600 animate-pulse"
                  >
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80 shadow-lg border-0 bg-white dark:bg-gray-800">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50">
                <div className="flex items-center justify-between">
                  <h3 className="font-bold text-gray-900 dark:text-white">
                    Notifications
                  </h3>
                  {unreadCount > 0 && (
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      {unreadCount} new
                    </Badge>
                  )}
                </div>
                {unreadCount > 0 && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    You have {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
                  </p>
                )}
              </div>
              <div className="max-h-96 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-6 text-center text-gray-500 dark:text-gray-400">
                    <Bell className="h-12 w-12 mx-auto mb-3 opacity-30" />
                    <p className="font-medium">No notifications yet</p>
                    <p className="text-sm mt-1">We'll notify you when something important happens</p>
                  </div>
                ) : (
                  notifications.slice(0, 5).map((notification) => (
                    <DropdownMenuItem
                      key={notification.id}
                      className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 border-b border-gray-100 dark:border-gray-700 last:border-b-0 transition-colors duration-150"
                      onClick={() => onNotificationClick(notification)}
                    >
                      <div className="flex items-start gap-3 w-full">
                        <div className={`w-3 h-3 rounded-full mt-1.5 flex-shrink-0 ${
                          notification.isRead ? 'bg-gray-300 dark:bg-gray-600' : 
                          notification.type === 'exam' ? 'bg-red-500' :
                          notification.type === 'task' ? 'bg-orange-500' :
                          notification.type === 'achievement' ? 'bg-green-500' :
                          'bg-blue-500'
                        }`} />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-2">
                            <p className={`font-medium text-sm truncate ${
                              notification.isRead ? 'text-gray-700 dark:text-gray-300' : 'text-gray-900 dark:text-white'
                            }`}>
                              {notification.title}
                            </p>
                            <span className="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0">
                              {formatNotificationTime(notification.timestamp)}
                            </span>
                          </div>
                          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                            {notification.message}
                          </p>
                          {notification.type && (
                            <Badge 
                              variant="outline" 
                              className="mt-2 text-xs capitalize"
                            >
                              {notification.type}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))
                )}
              </div>
              {notifications.length > 5 && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-center text-blue-600 dark:text-blue-400 cursor-pointer p-3 font-medium hover:bg-blue-50 dark:hover:bg-blue-900/20">
                    View all notifications
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

        </div>
      </div>
    </header>
  );
};

// Utility functions
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

function formatNotificationTime(timestamp: Date): string {
  const now = new Date();
  const diff = now.getTime() - timestamp.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return 'now';
  if (minutes < 60) return `${minutes}m`;
  if (hours < 24) return `${hours}h`;
  if (days < 7) return `${days}d`;
  return timestamp.toLocaleDateString();
}

export default DashboardHeader;
